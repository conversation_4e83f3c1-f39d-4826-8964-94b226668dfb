/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import CodePush from 'react-native-code-push';
import { useTranslation } from 'react-i18next';
import DrawerStack from './DrawerStack';
import Recommended from '../../../tablet-pages-offline/recommended';
import OrderDetailsOffline from '../../../tablet-pages-offline/order-details-offline';
import CartOffline from '../../../tablet-pages-offline/cart-offline';
import CollectionSetting from '../../../tablet-pages-backup/collection-setting';
import { setCodePushUpdate } from '../../../redux/features/common-slice';
import { useAppSelector } from '../../../redux/hooks';
import CatalogVariantOffline from '../../../tablet-pages-offline/catalog-variant-offline';
import { Navbar, NavbarControls } from '../../../components/tablet';
import NewProductsOffline from '../../../tablet-pages-offline/new-products-offline';
import FilterResult from '../../../tablet-pages-offline/filter-results';
import Language from '../../../tablet-pages-offline/language';
import DraftDetailsOffline from '../../../tablet-pages-offline/draft-details-offline';
import CategoryProducts from '../../../tablet-pages-offline/category-products';
import useManualSync from '../../../ManualSync';
import OrderDetails from '../../../tablet-pages-offline/order-details-online';
import RestockedProductsOffline from '../../../tablet-pages-offline/restocked-products-offline';

const Stack = createNativeStackNavigator();

const AppStack = () => {
	const dispatch = useDispatch();
	const { t } = useTranslation();
	// const { getCartItems } = useManualSync();
	useManualSync();
	const syncedOnce = useAppSelector((state) => state.auth.syncedOnce);
	const { isLoggedIn, userRoles, currentRole } = useAppSelector((state) => state.auth);

	// useEffect(() => {
	// 	console.log({ syncedOnce });
	// 	syncedOnce && getCartItems(); // To get all cart from local if sync is done on first launch
	// }, [syncedOnce]);

	useEffect(() => {
		checkCodePushUpdate();
	}, []);

	const checkCodePushUpdate = () => {
		// Check to see if there is still an update pending.
		CodePush.getUpdateMetadata(CodePush.UpdateState.PENDING).then((update) => {
			// console.log('update', update);
			if (update) {
				// There's a pending update, do we want to force a restart?
				dispatch(setCodePushUpdate(true));
			}
		});
	};

	// const initialRouteName = !isLoggedIn && userRoles.length ? 'ChooseAccount' : 'AppStack';

	return (
		<Stack.Navigator
			initialRouteName={'AppStack'}
			screenOptions={{
				orientation: 'landscape'
			}}
		>
			<Stack.Screen
				name="AppStack"
				component={DrawerStack}
				options={{
					headerShown: false
				}}
			/>
			{/* <Stack.Screen
				name="ChooseAccount"
				component={ChooseAccount}
				options={{
					headerShown: false
				}}
			/> */}
			<Stack.Screen
				name="Recommended"
				component={Recommended}
				options={{
					header: (props) => <Navbar title={t('recommended')} type='back' {...props} />
				}}
			/>
			<Stack.Screen
				name="NewProducts"
				component={NewProductsOffline}
				options={{
					header: (props) => <Navbar title={t('new_products')} type='back' {...props} />
				}}
			/>
			<Stack.Screen
				name="RestockedProducts"
				component={RestockedProductsOffline}
				options={{
					header: (props) => <Navbar title={t('restock_products')} type='back' {...props} />
				}}
			/>
			<Stack.Screen
				name="OrderDetails"
				component={OrderDetails}
				options={{
					header: (props) => <Navbar title={t('order_details')} type='back' {...props} />,
					headerShown: false
				}}
			/>
			<Stack.Screen
				name="DraftDetail"
				component={DraftDetailsOffline}
				options={{
					header: (props) => <Navbar title={t('draft')} type='back' {...props} />,
					headerShown: false
				}}
			/>
			<Stack.Screen
				name="CatalogVariant"
				component={CatalogVariantOffline}
				options={{
					header: (props) => <Navbar
						title={t('product_catalog')}
						type='back'
						{...props}
						headerRight={
							() => {
								return <NavbarControls screen={'CatalogVariant'} />;
							}
						}
					/>,
					animation: 'none'
				}}
			/>
			<Stack.Screen
				name="Cart"
				component={CartOffline}
				options={{
					headerShown: false,
					animation: 'none',
					header: (props) => <Navbar
						title={t('cart')}
						type='back'
						{...props}
						headerRight={
							() => {
								return <NavbarControls screen={'Cart'} />;
							}
						}
					/>
				}}
			/>
			<Stack.Screen
				name="CollectionSettings"
				component={CollectionSetting}
				options={{
					header: (props) => <Navbar
						title={t('collections')}
						type='back'
						{...props}
						headerRight={
							() => {
								return <NavbarControls screen={'CollectionSettings'} />;
							}
						}
					/>
				}}
			/>
			<Stack.Screen
				name="FilterResult"
				component={FilterResult}
				options={{
					header: (props) => <Navbar title={t('results')} type='back' {...props} />,
					animation: 'fade'
				}}
			/>
			<Stack.Screen
				name="Language"
				component={Language}
				options={{
					header: (props) => <Navbar title={t('app_language')} type='back' {...props} />,
					animation: 'fade'
				}}
			/>
			<Stack.Screen
				name="CategoryProducts"
				component={CategoryProducts}
				options={{
					header: (props) => <Navbar title={'Philips'} type='back' {...props} />,
					animation: 'fade'
				}}
			/>
		</Stack.Navigator>
	);
};

export default AppStack;
