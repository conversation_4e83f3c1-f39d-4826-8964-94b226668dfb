import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../store';

/* -------------------------------------------------------------------------- */
/*                  // This selector is only used in offline                  */
/* -------------------------------------------------------------------------- */

const selectCategoryState = (state: RootState) => state.category;
const selectSettingState = (state: RootState) => state.setting;

export const getNewProductsForTablet = createSelector(
	[selectCategoryState, selectSettingState],
	(category, setting) => {
		const masterSettings = setting.masterSettings;
		const allProducts = category.allProducts;
		const currentDate = new Date();

		const products = allProducts.length > 0 ? allProducts.filter((product: any) => {
			const Difference_In_Time = currentDate.getTime() - new Date(product.created_at).getTime();
			const diffInDays = Difference_In_Time / (1000 * 3600 * 24);
			return diffInDays <= masterSettings?.consider_new_item;
		}).sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()) : [];

		return products;
	}
);

export const getRestockedProductsForTablet = createSelector(
	[selectCategoryState],
	(category) => {
		const allProducts = category.allProducts;
		const products = allProducts.length > 0 ? allProducts.filter((product: any) => {
			return product.is_restocked;
		}).sort((a, b) => new Date(b.restocked_at).getTime() - new Date(a.restocked_at).getTime()) : [];

		return products;
	}
);

export const getAllProducts = createSelector(
	[selectCategoryState],
	(category) => {
		const allProducts = category.filtersApplied ? category.filterProducts : category.allProducts;
		if (category.filtersApplied && category.filterProducts?.length === 0) {
			return ['empty'];
		}
		return [...allProducts];
	}
);
