import { createSlice } from '@reduxjs/toolkit';
import { getNewProducts, getRestockedProducts } from '../apis/product';
import { getGroups } from '../../utils/groupSplit';

interface HomeState {
	newProducts: Array<any>;
	newProductsCount: number;
	loadingNewProducts: boolean;
	restockedProducts: Array<any>;
	restockedProductsCount: number;
	loadingRestockedProducts: boolean;
}

export const homeInitialState: HomeState = {
	newProducts: [],
	newProductsCount: 0,
	loadingNewProducts: false,
	restockedProducts: [],
	restockedProductsCount: 0,
	loadingRestockedProducts: false
};

const homeSlice = createSlice({
	name: 'home',
	initialState: homeInitialState,
	reducers: {
		clearNewProducts(state) {
			state.newProducts = [];
			state.newProductsCount = 0;
		},
		loaderNewProducts(state) {
			state.loadingNewProducts = true;
		},
		clearRestockedProducts(state) {
			state.restockedProducts = [];
			state.restockedProductsCount = 0;
		},
		loaderRestockedProducts(state) {
			state.loadingRestockedProducts = true;
		}
	},
	extraReducers(builder) {
		builder.addCase(getNewProducts.pending, (state) => {
			state.loadingNewProducts = true;
		});
		builder.addCase(getNewProducts.fulfilled, (state, action: any) => {
			const { data } = action.payload;
			const isFirstPage = action.meta.arg.page === 1;

			let products;
			if (isFirstPage) {
				// For page 1, replace existing data
				products = data.list || [];
			} else {
				// For subsequent pages, concatenate to existing data
				products = state.newProducts.concat(data.list || []);
			}

			state.newProducts = getGroups(products);
			state.newProductsCount = data.count;
			state.loadingNewProducts = false;
		});
		builder.addCase(getNewProducts.rejected, (state) => {
			state.loadingNewProducts = false;
		});
		builder.addCase(getRestockedProducts.pending, (state) => {
			state.loadingRestockedProducts = true;
		});
		builder.addCase(getRestockedProducts.fulfilled, (state, action: any) => {
			console.log('🚀 ~ builder.addCase ~ action.payload.data:', action.payload.data);
			const { data } = action.payload;
			const isFirstPage = action.meta.arg.page === 1;

			let products;
			if (isFirstPage) {
				// For page 1, replace existing data
				products = data.list || [];
			} else {
				// For subsequent pages, concatenate to existing data
				products = state.restockedProducts.concat(data.list || []);
			}

			state.restockedProducts = getGroups(products);
			state.restockedProductsCount = data.count;
			state.loadingRestockedProducts = false;
		});
		builder.addCase(getRestockedProducts.rejected, (state) => {
			state.loadingRestockedProducts = false;
		});
	}
});

export const { clearNewProducts, loaderNewProducts, clearRestockedProducts, loaderRestockedProducts } = homeSlice.actions;
export default homeSlice.reducer;