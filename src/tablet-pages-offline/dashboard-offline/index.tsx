import React, { useEffect, useState } from 'react';
import { ActivityIndicator, ScrollView, Text, View } from 'react-native';
import { useIsConnected } from 'react-native-offline';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { BrowseCategories, DealsView, NewProductsOffline, RestockedProductsOffline } from '../../components/tablet';
import { clearNewProducts } from '../../redux/features/newProductsOnline-slice';
import { useAppSelector } from '../../redux/hooks';
import { getPriceListId } from '../../redux/selectors';
import styles from './styles';
import { colors } from '../../utils/theme';
import { ProgressBar } from '../../components/common';


const DashboardOffline = () => {
	const { t } = useTranslation();
	const dispatch = useDispatch();
	const isConnected = useIsConnected();
	const priceListId = useAppSelector(getPriceListId);
	const activeCustomer = useAppSelector(state => state.customer.activeCustomer);
	// useProducts();
	const loadingData = useAppSelector(state => state.auth.loadingData);
	const syncedOnce = useAppSelector((state) => state.auth.syncedOnce);
	const progressCount = useAppSelector(state => state.image.progressCount);
	const [showMessage, setShowMessage] = useState(false);
	useEffect(() => {
		dispatch(clearNewProducts());
		// loadNewProducts();
	}, [activeCustomer, priceListId]);

	useEffect(() => {
		// To show successfull message if download is completed
		if (progressCount === 100) {
			setShowMessage(true);
			const timeoutToHideMessage = setTimeout(() => {
				setShowMessage(false);
			}, 2000);
			return () => clearTimeout(timeoutToHideMessage);
		}
	}, [progressCount === 100]);

	const SynchronizationLoader = () => (
		<View style={styles.loadingContainer}>
			<ActivityIndicator color={colors.black} size={'large'} />
			<Text style={styles.loadingText}>{t('offline_sync_loading_message')} 🕒</Text>
		</View>
	);

	return (

		<ScrollView
			contentContainerStyle={styles.container}
			showsVerticalScrollIndicator={false}
		>

			{!loadingData && progressCount !== 100 && progressCount >= 1 && !isConnected ?
				<View style={styles.progressBar}>
					<Text style={styles.downloadTitle}>{t('connect_to_internet_to_continue_download')}, {100 - progressCount}% {t('is_remaining')}.</Text>
					<ProgressBar percentage={`${progressCount}%`} />
				</View>
				: !loadingData && progressCount !== 100 && progressCount >= 1 ?
					<View style={styles.progressBar}>
						<Text style={styles.downloadTitle}>{t('Offline_download_progress')} ({progressCount}%)</Text>
						<ProgressBar percentage={`${progressCount}%`} />
					</View>
					: <></>
			}

			{
				showMessage ? <View style={styles.progressBar}>
					<Text style={styles.downloadTitle}>{t('Ready_for_offline')}</Text>
				</View> : <></>
			}

			{loadingData && !syncedOnce ? <SynchronizationLoader /> : <></>}
			<NewProductsOffline />
			<RestockedProductsOffline />
			<BrowseCategories />
			<DealsView />
		</ScrollView>
	);
};

export default DashboardOffline;
