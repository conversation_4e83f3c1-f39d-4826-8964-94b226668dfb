import { differenceInDays } from 'date-fns';
import { PRODUCT_TYPES } from '../constants';

export const filterProductsByPriceListId = (
	products: any,
	priceListId: string
) => {
	const filteredProducts: Array<any> = [];
	for (let index = 0; index < products.length; index++) {
		const x = products[index];
		const notVariant =
			x.type === PRODUCT_TYPES.SINGLE || x.type === PRODUCT_TYPES.PARENT;
		const hasPriceList = x.price_mappings.find(
			(price: any) =>
				price?.master_price_id === priceListId && price?.price > 0
		);
		if (hasPriceList && notVariant) {
			filteredProducts.push({
				_id: x._id,
				category_id: x.category_id,
				family_id: x.family_id,
				brand_id: x.brand_id,
				subcategory_id: x.subcategory_id,
				title: x.title,
				secondary_language_title: x.secondary_language_title,
				type: x.type,
				product_order: x.product_order,
				price_mappings: x.price_mappings,
				inventory_mappings: x.inventory_mappings,
				item_number: x.item_number,
				active_variant_item_numbers: x.active_variant_item_numbers,
				created_at: x.created_at,
				tags: x.tags,
				is_restocked: x.is_restocked,
				restocked_at: x.restocked_at
			});
		}
	}
	console.log('filteredProducts', filteredProducts.length);
	return filteredProducts.length ? filteredProducts : [];
};

export const filterProductsByPriceListIdAndInStock = (
	products: any,
	priceListId: string,
	branchId: string
) => {
	const filteredProducts: Array<any> = [];
	for (let index = 0; index < products.length; index++) {
		const x = products[index];
		const notVariant =
			x.type === PRODUCT_TYPES.SINGLE || x.type === PRODUCT_TYPES.PARENT;
		const hasPriceList = x.price_mappings.find(
			(price: any) =>
				price?.master_price_id === priceListId && price?.price > 0
		);

		const StockAvailable = () => {
			const branchInventory = x?.inventory_mappings?.find(
				(item: { branch_id: any }) => item.branch_id === branchId
			);
			if (branchInventory && branchInventory?.quantity === 0) {
				return false;
			} else {
				return true;
			}
		};
		if (hasPriceList && notVariant && StockAvailable()) {
			filteredProducts.push({
				_id: x._id,
				category_id: x.category_id,
				family_id: x.family_id,
				brand_id: x.brand_id,
				subcategory_id: x.subcategory_id,
				title: x.title,
				secondary_language_title: x.secondary_language_title,
				type: x.type,
				product_order: x.product_order,
				price_mappings: x.price_mappings,
				inventory_mappings: x.inventory_mappings,
				item_number: x.item_number,
				active_variant_item_numbers: x.active_variant_item_numbers,
				created_at: x.created_at,
				tags: x.tags,
				is_restocked: x.is_restocked,
				restocked_at: x.restocked_at
			});
		}
	}
	console.log('filteredProducts', filteredProducts.length);
	return filteredProducts.length ? filteredProducts : [];
};

export const filterVariantsOnly = (products: any, priceListId: string) => {
	const filteredProducts: Array<any> = [];
	for (let index = 0; index < products.length; index++) {
		const x = products[index];
		const isVariant = x.type === PRODUCT_TYPES.VARIANT;
		const hasPriceList = x.price_mappings.find(
			(price: any) =>
				price?.master_price_id === priceListId && price?.price > 0
		);
		// console.log('hasPriceList', hasPriceList);
		if (hasPriceList && isVariant) {
			filteredProducts.push(x);
		}
	}
	// console.log('filterVariantsOnly', filteredProducts.length);
	return filteredProducts.length ? filteredProducts : [];
};

const GetAllProductsByCategoryId = (allProducts: Array<any>) => {
	if (allProducts && allProducts?.length > 0) {
		console.log(
			'🚀 ~ GetAllProductsByCategoryId ~  allProducts?.length:',
			allProducts?.length
		);
		const allProductsByCategoryId: any = {};
		for (let i = 0; i < allProducts.length; i++) {
			const product = allProducts[i];
			const family_id = product?.family_id ? product?.family_id : null;
			const category_id = product?.category_id ? product?.category_id : null;
			const subcategory_id = product?.subcategory_id
				? product?.subcategory_id
				: null;
			const categoryKey = `${family_id}_${category_id}_${subcategory_id}`;
			// console.log(
			// 	'🚀 ~ GetAllProductsByCategoryId ~ categoryKey:',
			// 	categoryKey
			// );
			if (!allProductsByCategoryId[categoryKey]) {
				allProductsByCategoryId[categoryKey] = [];
			}
			allProductsByCategoryId[categoryKey].push(product);
		}
		// console.log(
		// 	'🚀 ~ GetAllProductsByCategoryId ~ allProductsByCategoryId:',
		// 	JSON.stringify(allProductsByCategoryId)
		// );
		console.log('GetallProductsByCategoryId filteration done ');
		return allProductsByCategoryId;
	} else {
		return {};
	}
};

export const getProductsInCategoryOrder = (
	categories: any,
	allProducts: any
) => {
	const products: any = [];
	const browseCategories: any = [];
	let allProductsByCategory = GetAllProductsByCategoryId(allProducts);
	// let allProductsCount = 0;
	categories.forEach((family: any) => {
		let totalProductsInFamily = 0;
		family.categoryList.forEach((category: any) => {
			category.subCategoryList.forEach((subCategory: any) => {
				/* -------------------------------------------------------------------------- */
				/*                            SubCategory Products                            */
				/* -------------------------------------------------------------------------- */
				let subCategorykey = `${family._id}_${category._id}_${subCategory._id}`;
				const subCategoryProducts = allProductsByCategory[subCategorykey];
				const sortedSubCategoryProducts = subCategoryProducts
					? [...subCategoryProducts].sort(
						(a: any, b: any) => a.product_order - b.product_order
					)
					: [];
				// console.log('🚀 ~ family.categoryList.forEach ~ sortedCategoryProducts?.length:', sortedSubCategoryProducts.length);
				// allProductsCount +=sortedSubCategoryProducts.length;
				if (sortedSubCategoryProducts?.length > 0) {
					totalProductsInFamily = totalProductsInFamily + sortedSubCategoryProducts?.length;
					products.push.apply(products, sortedSubCategoryProducts);
				}
			});

			/* -------------------------------------------------------------------------- */
			/*                              Category Products                             */
			/* -------------------------------------------------------------------------- */
			let categorykey = `${family._id}_${category._id}_null`;
			const categoryProducts = allProductsByCategory[categorykey];
			const sortedCategoryProducts = categoryProducts
				? [...categoryProducts].sort(
					(a: any, b: any) => a.product_order - b.product_order
				)
				: [];

			// console.log('🚀 ~ family.categoryList.forEach ~ sortedCategoryProducts?.length:', sortedCategoryProducts.length);
			// allProductsCount +=sortedCategoryProducts.length;
			if (sortedCategoryProducts?.length > 0) {
				totalProductsInFamily = totalProductsInFamily + sortedCategoryProducts?.length;
				products.push.apply(products, sortedCategoryProducts);
			}
		});

		/* -------------------------------------------------------------------------- */
		/*                               Family Products                              */
		/* -------------------------------------------------------------------------- */
		let familykey = `${family._id}_null_null`;
		const familyProducts = allProductsByCategory[familykey];
		const sortedFamilyProducts = familyProducts
			? [...familyProducts].sort(
				(a: any, b: any) => a.product_order - b.product_order
			)
			: [];
		if (sortedFamilyProducts?.length > 0) {
			totalProductsInFamily = totalProductsInFamily + sortedFamilyProducts?.length;
			products.push.apply(products, sortedFamilyProducts);
		}



		// console.log('🚀 ~ categories.forEach ~ familyAllProductsCount:', familyAllProductsCount);
		// console.log('🚀 ~ categories.forEach ~ allProductsCount:', allProductsCount);
		// allProductsCount +=familyAllProductsCount;
		if (totalProductsInFamily > 0) {
			browseCategories.push({
				...family,
				totalProducts: totalProductsInFamily
			});
		}
	});
	// console.log('🚀 ~ categories.forEach ~ allProductsCount:', allProductsCount);
	// console.log(JSON.stringify({categories, browseCategories}));
	return { products, browseCategories };
};

export const getGroupsForRealm = (productDetails: any) => {
	if (productDetails) {
		// console.log('productDetails', JSON.stringify(productDetails));
		if (productDetails.groups && productDetails.groups.values.length > 0) {
			const groups = productDetails.groups.values;
			const finalGroups: any = [];
			const productId = productDetails._id;
			groups.forEach((group: any) => {
				const groupId = group._id;
				const availableVariants: any = [];
				if (
					productDetails &&
					productDetails.variants &&
					productDetails.variants.values.length > 0
				) {
					const variants = productDetails.variants.values;
					variants.forEach((variant: any) => {
						const variantId = variant._id;
						const variantProductId = `${productId}_${variantId}_${groupId}`;
						if (
							productDetails.variantProducts &&
							productDetails.variantProducts.length > 0
						) {
							const availableVariant = productDetails.variantProducts.find(
								(item: any) => {
									// console.log('item in available variant', item);
									return (
										item.variant_id === variantProductId &&
										item.price_mappings.length > 0
									);
								}
							);
							const availableImage = availableVariant?.images.find(
								(item: any) => {
									return (
										item?.product_variant_id?.toString() ===
										availableVariant?._id?.toString()
									);
								}
							);
							if (availableImage) {
								const url = availableImage.s3_url;
								availableVariant.image_url = url;
								availableVariant.image = availableImage;
							}
							if (availableVariant) {
								availableVariants.push({
									...availableVariant,
									variant_value_id: variant
								});
							}
						}
					});
					if (availableVariants?.length > 0) {
						finalGroups.push({
							id: groupId,
							name: group.name,
							variants: availableVariants
						});
					}
				} else {
					finalGroups.push({ id: groupId, name: group.name, variants: [] });
				}
			});
			return finalGroups;
		} else {
			const variants = productDetails.variants.values;
			const productId = productDetails._id;
			const availableVariants: any = [];
			variants.forEach((variant: any) => {
				const variantId = variant._id;
				const variantProductId = `${productId}_${variantId}`;

				const availableVariant = productDetails.variantProducts.find(
					(item: any) => {
						return (
							item.variant_id === variantProductId &&
							item.price_mappings.length > 0
						);
					}
				);
				const availableImage = availableVariant?.images.find((item: any) => {
					return item?.product_variant_id?.toString() === availableVariant._id;
				});
				if (availableImage) {
					const url = availableImage.s3_url;
					availableVariant.image_url = url;
					availableVariant.image = availableImage;
				}
				if (availableVariant) {
					availableVariants.push({
						...availableVariant,
						variant_value_id: variant
					});
				}
			});
			const finalVariant = { key: 'variant', variants: availableVariants };
			return finalVariant;
		}
	} else {
		return [];
	}
};

export const filterAllProducts = (
	filters: any,
	allProducts: Array<any>,
	masterSettings: any,
	priceListId: string
) => {
	let products = allProducts;

	console.log('before filter', products.length);

	const priceRange = filters.priceRange;
	products = products.filter((product: any) => {
		const priceMap = product.price_mappings.find(
			(price: any) => price?.master_price_id?.toString() === priceListId
		);
		return (
			priceMap &&
			priceMap?.price >= priceRange.from &&
			priceMap?.price <= priceRange.to
		);
	});

	if (filters.tags.length > 0) {
		products = products.filter((product: any) => {
			const found = product?.tags?.some((r: any) => filters.tags.includes(r));
			return found;
		});
		// console.log('after tags', products.length);
	}

	if (filters.brands.length > 0) {
		products = products.filter((product: any) => {
			return filters?.brands.includes(product?.brand_id);
		});
		// console.log('after brand', products.length);
	}

	if (filters.productType && filters.productType.includes('NEW_PRODUCTS')) {
		products = products.filter((product: any) => {
			const diffInDays = differenceInDays(
				new Date(),
				new Date(product.created_at)
			);
			return diffInDays <= masterSettings?.consider_new_item;
		});
		// console.log('after NEW_PRODUCTS', products.length);
	}

	console.log('after filter', products.length);

	return products;
};
