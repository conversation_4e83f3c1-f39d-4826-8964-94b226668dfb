const Product = {
	name: 'products_20',
	properties: {
		_id: 'objectId?',
		brand_id: 'objectId?',
		category_id: 'objectId?',
		description: 'string?',
		family_id: 'objectId?',
		parent_id: 'objectId?',
		variant_id: 'string?',
		active_variant_item_numbers: 'string[]',
		subcategory_id: 'objectId?',
		title: 'string?',
		secondary_language_title: 'string?',
		type: 'string?',
		product_order: 'mixed?',
		is_active: 'bool?',
		is_deleted: 'bool?',
		tenant_id: 'int?',
		tags: 'objectId[]',
		packaging_map: 'products_20_packaging_map?',
		attributes: { type: 'list', objectType: 'products_20_attributes' },
		price_mappings: { type: 'list', objectType: 'products_20_price_mappings' },
		inventory_mappings: { type: 'list', objectType: 'products_20_inventory_mappings' },
		groups: 'products_20_groups?',
		variants: 'products_20_variants?',
		group_value_id: 'string?',
		variant_value_id: 'string?',
		variant_count: 'int?',
		variant_order: 'int?',
		item_number: 'string?',
		tax_id: 'objectId?',
		created_at: 'date?',
		is_restocked: 'bool?',
		restocked_at: 'date?'
	},
	primaryKey: '_id'
};

export { Product };
export * from './ProductAttributes';
export * from './ProductGroups';
export * from './ProductInventoryMappings';
export * from './ProductPackagingMap';
export * from './ProductPriceMappings';
export * from './ProductVariants';
export * from './VariantTypes';
export * from './FavoriteProducts';