import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Dimensions, Platform } from 'react-native';
import ConfettiCannon from 'react-native-confetti-cannon';
import Animated<PERSON><PERSON>ieView from 'lottie-react-native';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../constants';
import { colors } from '../../utils/theme';
import { useAppSelector } from '../../redux/hooks';
const { width } = Dimensions.get('window');

const RewardSuccessScanScreen = ({ onClose }: any) => {
	const memberScanReward = useAppSelector(
		(state) => state.reward.memberScanReward
	);
	const fadeAnim = useRef(new Animated.Value(0)).current;
	const progressAnim = useRef(new Animated.Value(0)).current;

	// Performance optimization for Android
	const isAndroid = Platform.OS === 'android';
	const shouldReduceAnimations = isAndroid;

	useEffect(() => {
		if (shouldReduceAnimations) {
			// Simplified animations for Android - faster and less resource intensive
			Animated.timing(fadeAnim, {
				toValue: 1,
				duration: 300, // Reduced from 800ms
				useNativeDriver: true
			}).start();

			// Simplified progress animation for Android
			Animated.timing(progressAnim, {
				toValue: 1,
				duration: 3000, // Reduced from 5000ms
				useNativeDriver: false
			}).start();

			const timer = setTimeout(() => {
				onClose?.();
			}, 3000); // Reduced from 5000ms

			return () => clearTimeout(timer);
		} else {
			// Full animations for iOS
			Animated.timing(fadeAnim, {
				toValue: 1,
				duration: 800,
				useNativeDriver: true
			}).start();

			Animated.timing(progressAnim, {
				toValue: 1,
				duration: 5000,
				useNativeDriver: false
			}).start();

			const timer = setTimeout(() => {
				onClose?.();
			}, 5000);

			return () => clearTimeout(timer);
		}
	}, [shouldReduceAnimations, fadeAnim, progressAnim, onClose]);

	const progressWidth = progressAnim.interpolate({
		inputRange: [0, 1],
		outputRange: ['100%', '0%']
	});

	return (
		<View style={styles.overlay}>
			<Animated.View style={[styles.card, { opacity: fadeAnim }]}>
				{/* <ImageBackground
					source={require('./assets/card-bg.png')}
					style={styles.card}
					imageStyle={{ borderRadius: 20 }}
				> */}
				<AnimatedLottieView
					source={require('../../assets/json/reward-coin.json')}
					autoPlay
					loop={!shouldReduceAnimations} // Disable looping on Android for better performance
					style={shouldReduceAnimations ? styles.coinAndroid : styles.coin}
					resizeMode='cover'
					speed={shouldReduceAnimations ? 1.5 : 1} // Faster animation on Android
					hardwareAccelerationAndroid={true} // Enable hardware acceleration on Android
				/>
				<Text style={styles.title}>Congratulations!</Text>
				<Text style={styles.subtitle}>{memberScanReward?.data?.message}</Text>

				<TouchableOpacity style={styles.button} onPress={onClose}>
					<Animated.View style={[styles.progressBar, { width: progressWidth }]} />
					<Text style={styles.buttonText}>Great!</Text>
				</TouchableOpacity>
				{/* </ImageBackground> */}
			</Animated.View>

			{/* Confetti LAST to render on top - Optimized for Android */}
			{shouldReduceAnimations ? (
				// Reduced confetti for Android performance
				<View style={StyleSheet.absoluteFillObject}>
					<ConfettiCannon
						count={50} // Reduced from 150 to 50
						origin={{ x: width / 2, y: 0 }}
						fadeOut
						autoStart
						fallSpeed={3500} // Faster fall speed for quicker completion
					/>
				</View>
			) : (
				// Full confetti for iOS
				<View style={StyleSheet.absoluteFillObject}>
					<ConfettiCannon
						count={150}
						origin={{ x: width / 2, y: 0 }}
						fadeOut
						autoStart
						fallSpeed={2500}
					/>
				</View>
			)}
		</View>
	);
};

const styles = StyleSheet.create({
	overlay: {
		flex: 1,
		width: '100%',
		backgroundColor: 'rgba(0, 0, 0, 0.5)',
		justifyContent: 'center',
		alignItems: 'center',
		position: 'relative'
	},
	card: {
		alignItems: 'center',
		padding: HORIZONTAL_DIMENS._30,
		borderRadius: HORIZONTAL_DIMENS._20,
		zIndex: 1,
		backgroundColor: '#fff',
		shadowColor: '#000',
		shadowOpacity: 0.2,
		shadowRadius: HORIZONTAL_DIMENS._10,
		elevation: 10
	},
	coin: {
		width: HORIZONTAL_DIMENS._250,
		height: HORIZONTAL_DIMENS._150,
		marginBottom: 20
	},
	coinAndroid: {
		width: HORIZONTAL_DIMENS._200, // Smaller size for Android
		height: HORIZONTAL_DIMENS._100,
		marginBottom: 20
	},
	title: {
		fontSize: HORIZONTAL_DIMENS._28,
		fontWeight: 'bold',
		color: '#333'
	},
	subtitle: {
		fontSize: HORIZONTAL_DIMENS._18,
		marginVertical: VERTICAL_DIMENS._10,
		color: '#666'
	},
	button: {
		marginTop: VERTICAL_DIMENS._20,
		backgroundColor: colors.vipLight,
		paddingHorizontal: HORIZONTAL_DIMENS._30,
		paddingVertical: VERTICAL_DIMENS._12,
		borderRadius: HORIZONTAL_DIMENS._25,
		overflow: 'hidden',
		position: 'relative',
		justifyContent: 'center',
		alignItems: 'center'
	},
	progressBar: {
		position: 'absolute',
		left: 0,
		top: 0,
		bottom: 0,
		backgroundColor: colors.vipDark,
		zIndex: -1
	},
	buttonText: {
		color: colors.black,
		fontSize: HORIZONTAL_DIMENS._16,
		fontWeight: 'bold',
		zIndex: 1
	}
});

export default RewardSuccessScanScreen;
