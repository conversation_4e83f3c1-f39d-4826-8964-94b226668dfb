import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Text, TouchableOpacity, View, StyleSheet, ActivityIndicator, Platform, Animated } from 'react-native';
import { CheckBox, PrimaryButton } from '../../components/common';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../constants';
import { colors, fonts } from '../../utils/theme';
import { useAppSelector } from '../../redux/hooks';
import { getLanguages } from '../../redux/selectors';
import { changeAppLanguage, needsRTLChange } from '../../utils/languageUtils';

const Language = () => {
	const { t, i18n } = useTranslation();
	const [language, setLanguage] = useState(i18n.language);
	const [isLoading, setIsLoading] = useState(false);
	const languages = useAppSelector(getLanguages);

	// Performance optimization for Android
	const isAndroid = Platform.OS === 'android';
	const fadeAnim = useRef(new Animated.Value(0)).current;
	const scaleAnim = useRef(new Animated.Value(0.8)).current;

	// Reduce animations on Android for better performance
	const shouldUseAnimations = !isAndroid || !isLoading;

	useEffect(() => {
		if (shouldUseAnimations && isLoading) {
			// Only animate on iOS or when not loading on Android
			Animated.parallel([
				Animated.timing(fadeAnim, {
					toValue: 1,
					duration: isAndroid ? 200 : 300, // Faster on Android
					useNativeDriver: true
				}),
				Animated.timing(scaleAnim, {
					toValue: 1,
					duration: isAndroid ? 200 : 300, // Faster on Android
					useNativeDriver: true
				})
			]).start();
		} else if (isLoading) {
			// Instant show on Android when loading
			fadeAnim.setValue(1);
			scaleAnim.setValue(1);
		}
	}, [isLoading, shouldUseAnimations, fadeAnim, scaleAnim, isAndroid]);

	const handleLanguage = async () => {
		try {
			setIsLoading(true);

			// On Android, add a small delay to ensure UI updates before heavy operations
			if (isAndroid) {
				await new Promise(resolve => setTimeout(resolve, 100));
			}

			// Check if RTL change is needed to show appropriate message
			const willRestart = needsRTLChange(language);

			if (willRestart) {
				// Show message that app will restart
				console.log('App will restart to apply language changes');
			}

			// Change the app language and handle RTL
			await changeAppLanguage(language);

			// If we reach here, no restart was needed
			setIsLoading(false);
		} catch (error) {
			console.error('Error changing language:', error);
			setIsLoading(false);
		}
	};

	return (
		<View style={styles.mainContainer}>
			{/* Loading Overlay - Optimized for Android Performance */}
			{isLoading && (
				<View style={styles.loadingOverlay}>
					{shouldUseAnimations ? (
						<Animated.View
							style={[
								styles.loadingContainer,
								{
									opacity: fadeAnim,
									transform: [{ scale: scaleAnim }]
								}
							]}
						>
							<ActivityIndicator
								size={isAndroid ? 'small' : 'large'}
								color={colors.primary}
							/>
							<Text style={styles.loadingText}>
								{needsRTLChange(language)
									? t('changing_language_restart', 'Changing language, app will restart...')
									: t('changing_language', 'Changing language...')
								}
							</Text>
						</Animated.View>
					) : (
						// Simplified version for Android during heavy operations
						<View style={styles.loadingContainer}>
							<ActivityIndicator
								size="small"
								color={colors.primary}
							/>
							<Text style={styles.loadingText}>
								{needsRTLChange(language)
									? t('changing_language_restart', 'Changing language, app will restart...')
									: t('changing_language', 'Changing language...')
								}
							</Text>
						</View>
					)}
				</View>
			)}

			<View style={[
				{ marginTop: VERTICAL_DIMENS._16, backgroundColor: colors.white },
				isLoading && styles.disabledContainer
			]}>
				{
					languages.map((item, index) => {
						const isLast = languages.length - 1 === index;
						return (
							<TouchableOpacity
								style={[styles.languageContainer, !isLast && styles.itemBottomBorder]}
								onPress={() => !isLoading && setLanguage(item.value)}
								key={item.value}
								disabled={isLoading}
							>
								<Text style={[
									styles.languageContentTitle,
									isLoading && styles.disabledText
								]}>
									{item.name}
								</Text>
								<CheckBox
									checked={language === item.value}
									onChange={() => !isLoading && setLanguage(item.value)}
								/>
							</TouchableOpacity>
						);
					})
				}
			</View>
			<View style={styles.buttonContainer}>
				<PrimaryButton
					title={t('confirm')}
					titleStyle={styles.titleStyle}
					onPress={handleLanguage}
					style={[styles.confirmButton, isLoading && styles.disabledButton]}
					disabled={isLoading}
					loading={isLoading}
				/>
			</View>
		</View>

	);
};
export default Language;

const styles = StyleSheet.create({
	mainContainer: {
		flex: 1
	},
	languageContainer: {
		paddingVertical: VERTICAL_DIMENS._14,
		flexDirection: 'row',
		marginHorizontal: HORIZONTAL_DIMENS._16,
		justifyContent: 'space-between'
	},
	itemBottomBorder: {
		borderBottomColor: colors.grey200,
		borderBottomWidth: 1
	},
	languageContentTitle: {
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._16,
		fontWeight: '400',
		color: colors.primary
	},
	confirmButton: {
		marginHorizontal: HORIZONTAL_DIMENS._40
	},
	buttonContainer: {
		position: 'absolute',
		width: '100%',
		bottom: VERTICAL_DIMENS._34
	},
	titleStyle: {
		textTransform: 'uppercase'
	},
	// Loading styles
	loadingOverlay: {
		position: 'absolute',
		top: 0,
		left: 0,
		right: 0,
		bottom: 0,
		backgroundColor: 'rgba(0, 0, 0, 0.5)',
		justifyContent: 'center',
		alignItems: 'center',
		zIndex: 1000
	},
	loadingContainer: {
		backgroundColor: colors.white,
		padding: HORIZONTAL_DIMENS._24,
		borderRadius: HORIZONTAL_DIMENS._12,
		alignItems: 'center',
		minWidth: 200
	},
	loadingText: {
		marginTop: VERTICAL_DIMENS._16,
		fontFamily: fonts.Montserrat.Medium,
		fontSize: HORIZONTAL_DIMENS._16,
		color: colors.primary,
		textAlign: 'center'
	},
	disabledContainer: {
		opacity: 0.6
	},
	disabledText: {
		color: colors.grey400
	},
	disabledButton: {
		opacity: 0.6
	}
});