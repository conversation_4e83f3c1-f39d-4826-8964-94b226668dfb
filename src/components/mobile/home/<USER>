import React, { memo, useCallback } from 'react';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { FlatList, I18nManager, StyleSheet, Text } from 'react-native';
import { Row } from 'react-native-col';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { CustomPressable } from '../../common';
import { SecondaryCardMobile } from '../../product-cards';
import { useAppSelector } from '../../../redux/hooks';
import { colors, fonts } from '../../../utils/theme';
import { NewProductsLoader } from '../../loader-mobile';
import { getPriceListId } from '../../../redux/selectors';
import { getPriceListObject } from '../../../utils/functions';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../../constants';
import { setProductListPrice, setSelectedGroup, setSelectedProduct, setSelectedvariant } from '../../../redux/features/productDetails-slice';

type FlatListItem = {
	item: any;
	index: number;
};

const RestockProducts = memo(() => {
	const { t } = useTranslation();
	const dispatch = useDispatch();
	const navigation = useNavigation<NativeStackNavigationProp<any>>();
	const priceListId = useAppSelector(getPriceListId);
	const { loadingRestockedProducts, restockedProducts } = useAppSelector(state => state.home);
	console.log('🚀 ~ RestockProducts ~ restockedProducts:', restockedProducts);

	const openAll = () => {
		navigation.navigate('RestockedProducts');
	};

	const openCatalogVariant = useCallback((item: any) => {
		const finalPriceListItem = getPriceListObject(item.price_mappings, priceListId);

		if (item?.group_value_id) {
			dispatch(setSelectedGroup(item?.group_value_id?._id));
			dispatch(setSelectedvariant(item?.variant_value_id));
		}
		dispatch(setSelectedProduct(item._id));
		// priceListId is undefined for new product
		if (finalPriceListItem) {
			dispatch(setProductListPrice(finalPriceListItem));
		}
		const itemId = item?._id;
		navigation.navigate('CatalogVariant', { itemId, priceMapping: item.price_mappings });
	}, [priceListId]);

	const renderProductCard = useCallback(({ item, index }: FlatListItem) => {
		const isEnd = index === restockedProducts.length - 1;
		return (
			<SecondaryCardMobile
				item={item}
				containerStyle={isEnd && styles.productLastItem}
				onPress={() => openCatalogVariant(item)}
			/>
		);
	}, []);

	const keyExtractor = useCallback((item: any, index: number) => `${item._id}-${index}`, []);

	if (loadingRestockedProducts) {
		return <NewProductsLoader />;
	}

	return (
		<>
			{loadingRestockedProducts === false && restockedProducts.length > 0 && (
				<>
					<Row.LR style={styles.header}>
						<Text style={styles.listHeader}>{t('restock_products')}</Text>
						<CustomPressable onPress={openAll}>
							<Text style={styles.viewAllText}>{t('view_all')}</Text>
						</CustomPressable>
					</Row.LR>
					<FlatList
						horizontal
						data={restockedProducts}
						renderItem={renderProductCard}
						keyExtractor={keyExtractor}
						contentContainerStyle={styles.listContainer}
						showsHorizontalScrollIndicator={false}
					/>
				</>
			)}
		</>
	);
});

const styles = StyleSheet.create({
	header: {
		marginHorizontal: HORIZONTAL_DIMENS._16,
		marginTop: VERTICAL_DIMENS._19
	},
	listContainer: {
		paddingLeft: HORIZONTAL_DIMENS._16,
		marginTop: VERTICAL_DIMENS._14
	},
	listHeader: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._20,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	viewAllText: {
		color: colors.secondary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._14,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	productLastItem: {
		marginRight: HORIZONTAL_DIMENS._16
	}
});

export { RestockProducts };