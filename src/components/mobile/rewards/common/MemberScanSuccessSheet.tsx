import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { SuccessIcon } from '../../../../assets/svgs/icons';
import { VERTICAL_DIMENS, HORIZONTAL_DIMENS } from '../../../../constants';
import { colors, fonts } from '../../../../utils/theme';
import { RewardBottomSheetModal } from '../../../common/RewardBottomSheetModal';

type Props = {
	successSheetRef: any,
};

const ¸ = ({ successSheetRef }: Props) => {
	const { t } = useTranslation();

	return (

		<RewardBottomSheetModal
			handleComponentColor
			ref={successSheetRef}
			height={VERTICAL_DIMENS._270}
			gradientColor={[colors.white, colors.white]}
		>
			<View style={styles.container}>
				<Text style={styles.title}>{t('status_success')}</Text>
				<Text style={styles.subTitle}>{t('member_scan_succefully')}</Text>
				<SuccessIcon fill={colors.darkGreen} />
			</View>
		</RewardBottomSheetModal>
	);
};

const styles = StyleSheet.create({
	container: {
		backgroundColor: colors.white,
		paddingBottom: VERTICAL_DIMENS._20,
		paddingTop: VERTICAL_DIMENS._15,
		paddingHorizontal: HORIZONTAL_DIMENS._80,
		alignItems: 'center'
	},
	title: {
		fontSize: HORIZONTAL_DIMENS._20,
		fontFamily: fonts.Montserrat.Regular,
		color: colors.darkGray,
		fontWeight: '600',
		textAlign: 'center'
	},
	subTitle: {
		fontSize: HORIZONTAL_DIMENS._16,
		fontFamily: fonts.Montserrat.Regular,
		color: colors.grey500,
		fontWeight: '400',
		textAlign: 'center',
		marginVertical: VERTICAL_DIMENS._20
	}
});

export { MemberScanSuccessSheet };
