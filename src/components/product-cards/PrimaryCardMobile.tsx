import React, { forwardRef, useState } from 'react';
import {
	I18nManager,
	LayoutChangeEvent,
	StyleProp,
	StyleSheet,
	Text,
	View,
	ViewStyle,
	Platform
} from 'react-native';
import { useDispatch } from 'react-redux';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { FavoriteToggle, ListingImage, PriceDecimal, SmartLabel } from '../common';
import { Variant } from '../../assets/svgs/icons';
import { FAVORITE_ACTION_TYPES, HORIZONTAL_DIMENS, PRODUCT_TYPES, VERTICAL_DIMENS } from '../../constants';
import { colors, fonts } from '../../utils/theme';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../redux/hooks';
import { getCatalogModeSetting, getPriceListId } from '../../redux/selectors';
import { addToFavoriteProduct } from '../../redux/apis/product';
import { getPriceListObject } from '../../utils/functions';

type Props = {
	item: any;
	containerStyle?: StyleProp<ViewStyle>;
	onPress?(): void;
	onLayout?: (event: LayoutChangeEvent) => void;
}

const PrimaryCardMobile = forwardRef(({ item, containerStyle, onPress, onLayout }: Props, ref: any) => {
	const { i18n } = useTranslation();
	const catalogMode = useAppSelector(getCatalogModeSetting);
	const currentRole = useAppSelector(state => state.auth.currentRole);
	const favoriteLoadingId = useAppSelector(state => state.favorite.favoriteLoadingId);
	const priceListId = useAppSelector(getPriceListId);
	const [productDetail, setproductDetail] = useState(item);
	React.useEffect(() => {
		if (item) {
			setproductDetail(item);
		}
	}, [item]);
	const dispatch = useDispatch();

	const isGroups = productDetail?.group_value_id;
	const productName = i18n.language === 'en'
		? isGroups
			? `${productDetail?.title}-${isGroups?.name}`
			: productDetail?.title
		: isGroups
			? `${productDetail?.secondary_language_title}-${isGroups?.name}`
			: productDetail.secondary_language_title;

	const imageUrl = isGroups ? isGroups?.group_cover_image?.s3_url || productDetail?.cover_image?.s3_url : productDetail?.cover_image?.s3_url;
	const imageName = isGroups ? isGroups?.group_cover_image?.image_name || productDetail?.cover_image?.image_name : productDetail?.cover_image?.image_name;

	const favoriteHandle = async () => {
		const requestBody = {
			tenantId: currentRole?.tenant_id?._id,
			type: productDetail.is_favorite ? FAVORITE_ACTION_TYPES.UNFAVORITE : FAVORITE_ACTION_TYPES.FAVORITE,
			ids: [productDetail?._id]
		};

		await dispatch(addToFavoriteProduct(requestBody));
		setproductDetail({
			...productDetail,
			is_favorite: !productDetail.is_favorite
		});
	};

	return (
		<TouchableOpacity
			style={[styles.productCard, containerStyle]}
			activeOpacity={Platform.OS === 'ios' ? 0.8 : 1}
			onPress={onPress}
			onLayout={onLayout}
			ref={ref}
		>
			<ListingImage imageName={imageName} imageUrl={imageUrl} />

			<SmartLabel product={productDetail} containerStlye={styles.tagContainer} />

			<View style={styles.contentContainer}>
				{/* Product Title */}
				<Text style={styles.prodcutTitle} numberOfLines={2}>
					{productName}
				</Text>
				{/* Price Info */}
				<View style={[styles.priceView, catalogMode && styles.hidePrice]}>
					{
						productDetail.type === PRODUCT_TYPES.SINGLE && (
							<PriceDecimal
								value={(productDetail.price_mappings && productDetail.price_mappings.length > 0) ? getPriceListObject(productDetail.price_mappings, priceListId)?.price : 'NA'}
								style={styles.productPrice}
							/>
						)
					}
					{
						productDetail.type === PRODUCT_TYPES.PARENT && (
							<PriceDecimal
								value={(productDetail.price_mappings && productDetail.price_mappings.length > 0) ? getPriceListObject(productDetail.price_mappings, priceListId)?.price : 'NA'}
								style={styles.productPrice}
							/>
						)
					}
					{
						productDetail.originalPrice && <PriceDecimal
							value={productDetail.originalPrice}
							style={styles.productOriginalPrice}
						/>
					}
				</View>
				{
					productDetail.type === PRODUCT_TYPES.PARENT && <Variant style={styles.variantIcon} fill={colors.grey400} height={20} width={20} />
				}
				<FavoriteToggle
					productId={productDetail?._id}
					loadingId={favoriteLoadingId}
					onPress={favoriteHandle}
					isFavorite={productDetail.is_favorite}
				/>
			</View>
		</TouchableOpacity>
	);
});

const styles = StyleSheet.create({
	productCard: {
		backgroundColor: colors.white,
		borderRadius: 12,
		marginRight: HORIZONTAL_DIMENS._8,
		shadowColor: colors.black,
		shadowOffset: {
			width: 0,
			height: 0
		},
		shadowOpacity: 0.08,
		shadowRadius: 3,
		elevation: 0,
		width: HORIZONTAL_DIMENS._168,
		// height: VERTICAL_DIMENS._258,
		height: VERTICAL_DIMENS._244,
		overflow: 'hidden'
	},
	imageContainer: {
		height: VERTICAL_DIMENS._168,
		width: HORIZONTAL_DIMENS._168
	},
	contentContainer: {
		//width: HORIZONTAL_DIMENS._168,
		// height: VERTICAL_DIMENS._76,
		borderRadius: 12,
		shadowOffset: {
			width: 0,
			height: -1
		},
		shadowColor: colors.black,
		shadowOpacity: 0.08,
		shadowRadius: 3,
		elevation: 3,
		backgroundColor: colors.white,
		height: VERTICAL_DIMENS._76
	},
	prodcutTitle: {
		alignSelf: 'flex-start',
		color: colors.primary,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		fontSize: HORIZONTAL_DIMENS._12,
		lineHeight: VERTICAL_DIMENS._15,
		height: VERTICAL_DIMENS._36,
		marginTop: VERTICAL_DIMENS._16,
		paddingHorizontal: HORIZONTAL_DIMENS._8,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	priceView: {
		position: 'absolute',
		flexDirection: 'row',
		paddingHorizontal: HORIZONTAL_DIMENS._8,
		// marginTop: VERTICAL_DIMENS._2,
		// backgroundColor:'lightpink',
		bottom: VERTICAL_DIMENS._10
	},
	productPrice: {
		color: colors.darkGray,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._14
	},
	productOriginalPrice: {
		color: colors.grey400,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		fontSize: HORIZONTAL_DIMENS._12,
		marginLeft: HORIZONTAL_DIMENS._10,
		top: VERTICAL_DIMENS._1,
		textDecorationLine: 'line-through'
	},
	tagContainer: {
		position: 'absolute',
		top: VERTICAL_DIMENS._8,
		left: HORIZONTAL_DIMENS._8
	},
	variantIcon: {
		position: 'absolute',
		bottom: VERTICAL_DIMENS._10,
		right: HORIZONTAL_DIMENS._12
	},
	hidePrice: {
		display: 'none'
	}

});

export { PrimaryCardMobile };
